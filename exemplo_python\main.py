#!/usr/bin/env python3
"""
Exemplo de Aplicativo Python
Aplicativo simples para testar o Gerador de Executáveis
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

class ExemploApp:
    """Aplicativo de exemplo"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Exemplo Python - Gerador de Executáveis")
        self.root.geometry("400x300")
        
        self.create_widgets()
        
    def create_widgets(self):
        """Criar widgets da interface"""
        
        # Título
        title_label = tk.Label(
            self.root,
            text="🚀 Exemplo Python",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=20)
        
        # Descrição
        desc_label = tk.Label(
            self.root,
            text="Este é um aplicativo de exemplo\npara testar o Gerador de Executáveis",
            justify="center"
        )
        desc_label.pack(pady=10)
        
        # Informações do sistema
        info_frame = tk.Frame(self.root)
        info_frame.pack(pady=20)
        
        tk.Label(info_frame, text=f"Python: {sys.version_info.major}.{sys.version_info.minor}").pack()
        tk.Label(info_frame, text=f"Sistema: {os.name}").pack()
        tk.Label(info_frame, text=f"Diretório: {os.getcwd()}").pack()
        
        # Botões
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        hello_btn = tk.Button(
            button_frame,
            text="Dizer Olá",
            command=self.say_hello,
            bg="#4CAF50",
            fg="white",
            padx=20
        )
        hello_btn.pack(side="left", padx=10)
        
        info_btn = tk.Button(
            button_frame,
            text="Informações",
            command=self.show_info,
            bg="#2196F3",
            fg="white",
            padx=20
        )
        info_btn.pack(side="left", padx=10)
        
        quit_btn = tk.Button(
            button_frame,
            text="Sair",
            command=self.root.quit,
            bg="#f44336",
            fg="white",
            padx=20
        )
        quit_btn.pack(side="left", padx=10)
        
    def say_hello(self):
        """Mostrar mensagem de olá"""
        messagebox.showinfo("Olá!", "Olá! Este executável foi gerado com sucesso! 🎉")
        
    def show_info(self):
        """Mostrar informações do aplicativo"""
        info = f"""
Aplicativo de Exemplo Python

Versão: 1.0.0
Python: {sys.version}
Executável: {sys.executable}
Argumentos: {sys.argv}

Este aplicativo foi criado para testar o
Gerador de Executáveis.
        """
        messagebox.showinfo("Informações", info)
        
    def run(self):
        """Executar aplicativo"""
        self.root.mainloop()

def main():
    """Função principal"""
    print("Iniciando aplicativo de exemplo...")
    
    app = ExemploApp()
    app.run()
    
    print("Aplicativo finalizado.")

if __name__ == "__main__":
    main()
