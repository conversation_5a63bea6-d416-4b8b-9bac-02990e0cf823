"""
Gerenciador de Configurações
Responsável por carregar, salvar e gerenciar configurações do aplicativo
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional

class ConfigManager:
    """Gerenciador de configurações do aplicativo"""
    
    def __init__(self):
        self.config_dir = Path.home() / '.gerador_executavel'
        self.config_file = self.config_dir / 'config.json'
        self.templates_dir = self.config_dir / 'templates'
        
        # Criar diretórios se não existirem
        self.config_dir.mkdir(exist_ok=True)
        self.templates_dir.mkdir(exist_ok=True)
        
        # Configurações padrão
        self.default_config = {
            'last_project_path': '',
            'output_directory': str(Path.home() / 'Desktop' / 'Executaveis'),
            'language_settings': {
                'python': {
                    'use_pyinstaller': True,
                    'one_file': True,
                    'console': False,
                    'icon_path': '',
                    'additional_args': []
                },
                'nodejs': {
                    'use_pkg': True,
                    'targets': ['node18-win-x64'],
                    'compress': True
                },
                'java': {
                    'use_maven': True,
                    'main_class': '',
                    'jar_name': ''
                },
                'dotnet': {
                    'framework': 'net6.0',
                    'runtime': 'win-x64',
                    'self_contained': True
                },
                'go': {
                    'build_flags': ['-ldflags', '-w -s'],
                    'target_os': 'windows',
                    'target_arch': 'amd64'
                },
                'rust': {
                    'release_mode': True,
                    'target': 'x86_64-pc-windows-msvc'
                }
            },
            'ui_settings': {
                'theme': 'default',
                'window_size': '800x600',
                'remember_window_position': True,
                'show_advanced_options': False
            },
            'build_settings': {
                'clean_before_build': True,
                'open_output_folder': True,
                'backup_original': False,
                'parallel_builds': True
            }
        }
        
        # Carregar configurações
        self.config = self.load_config()
        
    def load_config(self) -> Dict[str, Any]:
        """Carregar configurações do arquivo"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    
                # Mesclar com configurações padrão
                config = self.default_config.copy()
                self._deep_update(config, loaded_config)
                return config
            else:
                # Criar arquivo de configuração padrão
                self.save_config(self.default_config)
                return self.default_config.copy()
                
        except Exception as e:
            print(f"Erro ao carregar configurações: {e}")
            return self.default_config.copy()
            
    def save_config(self, config: Optional[Dict[str, Any]] = None):
        """Salvar configurações no arquivo"""
        try:
            config_to_save = config or self.config
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Erro ao salvar configurações: {e}")
            
    def get(self, key: str, default: Any = None) -> Any:
        """Obter valor de configuração usando notação de ponto"""
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key: str, value: Any):
        """Definir valor de configuração usando notação de ponto"""
        keys = key.split('.')
        config = self.config
        
        # Navegar até o penúltimo nível
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
            
        # Definir valor
        config[keys[-1]] = value
        
        # Salvar automaticamente
        self.save_config()
        
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """Atualizar dicionário recursivamente"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
                
    def get_language_config(self, language: str) -> Dict[str, Any]:
        """Obter configurações específicas de uma linguagem"""
        return self.config.get('language_settings', {}).get(language, {})
        
    def set_language_config(self, language: str, config: Dict[str, Any]):
        """Definir configurações específicas de uma linguagem"""
        if 'language_settings' not in self.config:
            self.config['language_settings'] = {}
        self.config['language_settings'][language] = config
        self.save_config()
        
    def reset_to_defaults(self):
        """Resetar configurações para padrão"""
        self.config = self.default_config.copy()
        self.save_config()
