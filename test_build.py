#!/usr/bin/env python3
"""
Script de teste para verificar se a geração de executável funciona
"""

import sys
import os
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from core.project_detector import ProjectDetector
from generators.generator_factory import GeneratorFactory
from utils.logger import setup_logger

def test_python_build():
    """Testar build de projeto Python"""
    
    logger = setup_logger()
    
    # Caminhos
    project_path = Path(__file__).parent / 'exemplo_python'
    output_path = Path(__file__).parent / 'output_test'
    
    print(f"🧪 Testando build do projeto Python...")
    print(f"   Projeto: {project_path}")
    print(f"   Saída: {output_path}")
    
    # Verificar se projeto existe
    if not project_path.exists():
        print("❌ Projeto de exemplo não encontrado!")
        return False
        
    # Detectar projeto
    detector = ProjectDetector()
    project_info = detector.detect_project(str(project_path))
    
    print(f"   Tipo detectado: {project_info['type']}")
    print(f"   Confiança: {project_info['confidence']}%")
    
    if project_info['type'] != 'python':
        print("❌ Projeto não foi detectado como Python!")
        return False
        
    # Configurar opções de build
    options = {
        'use_pyinstaller': True,
        'one_file': True,
        'no_console': False,
        'main_file': 'main.py'
    }
    
    # Criar gerador
    generator = GeneratorFactory.create_generator(
        'python', str(project_path), str(output_path), options, logger
    )
    
    if not generator:
        print("❌ Não foi possível criar gerador!")
        return False
        
    # Verificar dependências
    missing_deps = generator.check_dependencies()
    if missing_deps:
        print(f"❌ Dependências faltando: {', '.join(missing_deps)}")
        return False
        
    print("✅ Dependências verificadas")
    
    # Callback de progresso
    def progress_callback(percentage, message):
        print(f"   Progresso: {percentage}% - {message}")
        
    generator.set_progress_callback(progress_callback)
    
    # Gerar executável
    print("🔨 Iniciando geração...")
    success = generator.generate()
    
    if success:
        print("✅ Executável gerado com sucesso!")
        
        # Verificar se arquivo foi criado
        exe_info = generator.get_executable_info()
        if 'executable_path' in exe_info:
            exe_path = Path(exe_info['executable_path'])
            if exe_path.exists():
                print(f"   Arquivo: {exe_path}")
                print(f"   Tamanho: {exe_info.get('size_mb', 'N/A')} MB")
                return True
            else:
                print("❌ Arquivo executável não encontrado!")
                return False
        else:
            print("⚠️  Executável gerado mas caminho não informado")
            return True
    else:
        print("❌ Falha na geração do executável!")
        return False

def main():
    """Função principal"""
    
    print("🚀 Teste de Geração de Executável")
    print("=" * 50)
    
    try:
        success = test_python_build()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 Teste concluído com sucesso!")
            return 0
        else:
            print("❌ Teste falhou!")
            return 1
            
    except Exception as e:
        print(f"\n❌ Erro durante teste: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
