"""
Janela Principal da Interface Gráfica
Interface principal do Gerador de Executáveis
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
from pathlib import Path
from typing import Optional

from ..core.config_manager import ConfigManager
from ..core.project_detector import ProjectDetector
from ..generators.generator_factory import GeneratorFactory
from ..utils.logger import LogCapture
from .components.project_selector import ProjectSelector
from .components.build_options import BuildOptions
from .components.log_viewer import LogViewer
from .components.progress_bar import ProgressBar

class MainWindow:
    """Janela principal do aplicativo"""
    
    def __init__(self, root: tk.Tk, config_manager: ConfigMana<PERSON>, logger):
        self.root = root
        self.config_manager = config_manager
        self.logger = logger
        self.log_capture = LogCapture(logger)
        self.project_detector = ProjectDetector()
        
        # Variáveis de estado
        self.current_project_path = tk.StringVar()
        self.current_project_type = tk.StringVar()
        self.output_directory = tk.StringVar(value=config_manager.get('output_directory'))
        self.is_building = False
        
        # Criar interface
        self.create_widgets()
        self.setup_layout()
        self.bind_events()
        
        # Carregar configurações
        self.load_settings()
        
    def create_widgets(self):
        """Criar widgets da interface"""
        
        # Frame principal
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # Notebook para abas
        self.notebook = ttk.Notebook(self.main_frame)
        
        # Aba Principal
        self.main_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.main_tab, text="Projeto")
        
        # Aba de Configurações
        self.config_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.config_tab, text="Configurações")
        
        # Aba de Logs
        self.log_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.log_tab, text="Logs")
        
        # === ABA PRINCIPAL ===
        
        # Seletor de projeto
        self.project_selector = ProjectSelector(
            self.main_tab, 
            self.current_project_path,
            self.current_project_type,
            self.on_project_selected
        )
        
        # Opções de build
        self.build_options = BuildOptions(
            self.main_tab,
            self.config_manager,
            self.current_project_type
        )
        
        # Diretório de saída
        self.output_frame = ttk.LabelFrame(self.main_tab, text="Diretório de Saída", padding="5")
        
        self.output_entry = ttk.Entry(
            self.output_frame, 
            textvariable=self.output_directory,
            width=50
        )
        
        self.output_browse_btn = ttk.Button(
            self.output_frame,
            text="Procurar",
            command=self.browse_output_directory
        )
        
        # Barra de progresso
        self.progress_bar = ProgressBar(self.main_tab)
        
        # Botões de ação
        self.action_frame = ttk.Frame(self.main_tab)
        
        self.build_btn = ttk.Button(
            self.action_frame,
            text="Gerar Executável",
            command=self.start_build,
            style="Accent.TButton"
        )
        
        self.cancel_btn = ttk.Button(
            self.action_frame,
            text="Cancelar",
            command=self.cancel_build,
            state="disabled"
        )
        
        self.open_output_btn = ttk.Button(
            self.action_frame,
            text="Abrir Pasta de Saída",
            command=self.open_output_folder
        )
        
        # === ABA DE LOGS ===
        
        self.log_viewer = LogViewer(self.log_tab, self.log_capture)
        
    def setup_layout(self):
        """Configurar layout dos widgets"""
        
        # Frame principal
        self.main_frame.grid(row=0, column=0, sticky="nsew")
        
        # Configurar redimensionamento
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(0, weight=1)
        self.main_frame.rowconfigure(0, weight=1)
        
        # Notebook
        self.notebook.grid(row=0, column=0, sticky="nsew")
        
        # === LAYOUT ABA PRINCIPAL ===
        
        # Configurar redimensionamento da aba principal
        self.main_tab.columnconfigure(0, weight=1)
        
        row = 0
        
        # Seletor de projeto
        self.project_selector.grid(row=row, column=0, sticky="ew", pady=(0, 10))
        row += 1
        
        # Opções de build
        self.build_options.grid(row=row, column=0, sticky="ew", pady=(0, 10))
        row += 1
        
        # Diretório de saída
        self.output_frame.grid(row=row, column=0, sticky="ew", pady=(0, 10))
        self.output_frame.columnconfigure(0, weight=1)
        
        self.output_entry.grid(row=0, column=0, sticky="ew", padx=(0, 5))
        self.output_browse_btn.grid(row=0, column=1)
        row += 1
        
        # Barra de progresso
        self.progress_bar.grid(row=row, column=0, sticky="ew", pady=(0, 10))
        row += 1
        
        # Botões de ação
        self.action_frame.grid(row=row, column=0, sticky="ew")
        
        self.build_btn.pack(side="left", padx=(0, 5))
        self.cancel_btn.pack(side="left", padx=(0, 5))
        self.open_output_btn.pack(side="right")
        
        # === LAYOUT ABA DE LOGS ===
        
        self.log_tab.columnconfigure(0, weight=1)
        self.log_tab.rowconfigure(0, weight=1)
        self.log_viewer.grid(row=0, column=0, sticky="nsew")
        
    def bind_events(self):
        """Vincular eventos"""
        
        # Salvar configurações ao fechar
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Atualizar configurações quando mudar diretório de saída
        self.output_directory.trace_add("write", self.on_output_directory_changed)
        
    def load_settings(self):
        """Carregar configurações salvas"""
        
        # Carregar último projeto
        last_project = self.config_manager.get('last_project_path')
        if last_project and os.path.exists(last_project):
            self.current_project_path.set(last_project)
            self.project_selector.detect_project_type()
            
    def on_project_selected(self, project_path: str, project_type: str):
        """Callback quando projeto é selecionado"""
        
        self.logger.info(f"Projeto selecionado: {project_path} (Tipo: {project_type})")
        
        # Salvar último projeto
        self.config_manager.set('last_project_path', project_path)
        
        # Atualizar opções de build
        self.build_options.update_for_project_type(project_type)
        
    def browse_output_directory(self):
        """Procurar diretório de saída"""
        
        directory = filedialog.askdirectory(
            title="Selecionar Diretório de Saída",
            initialdir=self.output_directory.get()
        )
        
        if directory:
            self.output_directory.set(directory)
            
    def on_output_directory_changed(self, *args):
        """Callback quando diretório de saída muda"""
        self.config_manager.set('output_directory', self.output_directory.get())
        
    def start_build(self):
        """Iniciar processo de build"""
        
        # Validar entrada
        if not self.current_project_path.get():
            messagebox.showerror("Erro", "Selecione um projeto primeiro")
            return
            
        if not self.current_project_type.get():
            messagebox.showerror("Erro", "Tipo de projeto não detectado")
            return
            
        # Criar diretório de saída se não existir
        output_dir = Path(self.output_directory.get())
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Atualizar interface
        self.is_building = True
        self.build_btn.config(state="disabled")
        self.cancel_btn.config(state="normal")
        self.progress_bar.start()
        
        # Iniciar build em thread separada
        build_thread = threading.Thread(target=self._build_worker, daemon=True)
        build_thread.start()
        
    def _build_worker(self):
        """Worker para processo de build"""

        try:
            self.logger.info("Iniciando geração de executável...")

            # Obter configurações
            project_path = self.current_project_path.get()
            project_type = self.current_project_type.get()
            output_path = self.output_directory.get()

            # Obter opções de build
            build_options = self.build_options.get_options()

            # Criar gerador
            generator = GeneratorFactory.create_generator(
                project_type, project_path, output_path, build_options, self.logger
            )

            if not generator:
                raise Exception(f"Gerador não disponível para {project_type}")

            # Configurar callback de progresso
            def progress_callback(percentage, message):
                self.root.after(0, lambda: self.progress_bar.set_progress(percentage, message))

            generator.set_progress_callback(progress_callback)

            # Verificar dependências
            missing_deps = generator.check_dependencies()
            if missing_deps:
                raise Exception(f"Dependências faltando: {', '.join(missing_deps)}")

            # Gerar executável
            success = generator.generate()

            if success:
                self.logger.info("Executável gerado com sucesso!")

                # Obter informações do executável
                exe_info = generator.get_executable_info()
                self.root.after(0, lambda: self._build_completed(exe_info))
            else:
                raise Exception("Falha na geração do executável")

        except Exception as e:
            self.logger.error(f"Erro durante build: {e}")
            self.root.after(0, lambda: self._build_failed(str(e)))
            
    def _build_completed(self, exe_info=None):
        """Callback quando build é concluído"""

        self.is_building = False
        self.build_btn.config(state="normal")
        self.cancel_btn.config(state="disabled")
        self.progress_bar.stop()

        # Criar mensagem com informações do executável
        message = "Executável gerado com sucesso!"
        if exe_info:
            if 'size_mb' in exe_info:
                message += f"\nTamanho: {exe_info['size_mb']} MB"
            if 'executable_path' in exe_info:
                message += f"\nLocal: {exe_info['executable_path']}"

        messagebox.showinfo("Sucesso", message)

        # Abrir pasta de saída se configurado
        if self.config_manager.get('build_settings.open_output_folder', True):
            self.open_output_folder()
            
    def _build_failed(self, error_message: str):
        """Callback quando build falha"""
        
        self.is_building = False
        self.build_btn.config(state="normal")
        self.cancel_btn.config(state="disabled")
        self.progress_bar.stop()
        
        messagebox.showerror("Erro", f"Falha ao gerar executável:\n{error_message}")
        
    def cancel_build(self):
        """Cancelar processo de build"""
        
        self.is_building = False
        self.logger.info("Build cancelado pelo usuário")
        
    def open_output_folder(self):
        """Abrir pasta de saída no explorador"""
        
        output_path = Path(self.output_directory.get())
        if output_path.exists():
            os.startfile(str(output_path))
        else:
            messagebox.showwarning("Aviso", "Diretório de saída não existe")
            
    def on_closing(self):
        """Callback ao fechar aplicativo"""
        
        if self.is_building:
            if messagebox.askokcancel("Fechar", "Build em andamento. Deseja cancelar e fechar?"):
                self.cancel_build()
                self.root.destroy()
        else:
            self.root.destroy()
