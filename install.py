#!/usr/bin/env python3
"""
Script de Instalação do Gerador de Executáveis
Instala dependências e configura o ambiente
"""

import subprocess
import sys
import os
from pathlib import Path

def check_python_version():
    """Verificar versão do Python"""
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 ou superior é necessário")
        print(f"Versão atual: {sys.version}")
        return False
        
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detectado")
    return True

def install_dependencies():
    """Instalar dependências do requirements.txt"""
    
    print("\n📦 Instalando dependências...")
    
    try:
        # Atualizar pip primeiro
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                      check=True, capture_output=True)
        
        # Instalar dependências
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True)
        
        print("✅ Dependências instaladas com sucesso")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro ao instalar dependências: {e}")
        return False

def check_optional_tools():
    """Verificar ferramentas opcionais"""
    
    print("\n🔧 Verificando ferramentas opcionais...")
    
    tools = {
        'Node.js': 'node',
        'npm': 'npm',
        'Java': 'java',
        'Maven': 'mvn',
        'Go': 'go',
        'Rust': 'cargo',
        '.NET': 'dotnet'
    }
    
    available_tools = []
    
    for tool_name, command in tools.items():
        try:
            subprocess.run([command, '--version'], 
                          capture_output=True, check=True)
            print(f"✅ {tool_name} disponível")
            available_tools.append(tool_name)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"⚠️  {tool_name} não encontrado (opcional)")
            
    return available_tools

def create_desktop_shortcut():
    """Criar atalho na área de trabalho (Windows)"""
    
    if os.name != 'nt':
        return False
        
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "Gerador de Executáveis.lnk")
        target = sys.executable
        wDir = str(Path(__file__).parent)
        arguments = str(Path(__file__).parent / "main.py")
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = target
        shortcut.Arguments = arguments
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = target
        shortcut.save()
        
        print("✅ Atalho criado na área de trabalho")
        return True
        
    except ImportError:
        print("⚠️  Não foi possível criar atalho (winshell não disponível)")
        return False
    except Exception as e:
        print(f"⚠️  Erro ao criar atalho: {e}")
        return False

def run_tests():
    """Executar testes básicos"""
    
    print("\n🧪 Executando testes básicos...")
    
    try:
        result = subprocess.run([sys.executable, 'run_tests.py'], 
                               capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Todos os testes passaram")
            return True
        else:
            print("❌ Alguns testes falharam:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"⚠️  Erro ao executar testes: {e}")
        return False

def main():
    """Função principal de instalação"""
    
    print("🚀 Instalador do Gerador de Executáveis")
    print("=" * 50)
    
    # Verificar Python
    if not check_python_version():
        return 1
        
    # Instalar dependências
    if not install_dependencies():
        return 1
        
    # Verificar ferramentas opcionais
    available_tools = check_optional_tools()
    
    # Executar testes
    tests_passed = run_tests()
    
    # Criar atalho (Windows)
    if os.name == 'nt':
        create_desktop_shortcut()
        
    print("\n" + "=" * 50)
    print("📋 Resumo da Instalação:")
    print(f"✅ Python: {sys.version_info.major}.{sys.version_info.minor}")
    print(f"✅ Dependências: Instaladas")
    print(f"✅ Ferramentas disponíveis: {', '.join(available_tools) if available_tools else 'Nenhuma'}")
    print(f"{'✅' if tests_passed else '⚠️ '} Testes: {'Passaram' if tests_passed else 'Falharam'}")
    
    print("\n🎉 Instalação concluída!")
    print("\nPara executar o aplicativo:")
    print("  python main.py")
    
    if not tests_passed:
        print("\n⚠️  Alguns testes falharam, mas o aplicativo deve funcionar.")
        
    return 0

if __name__ == '__main__':
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n❌ Instalação cancelada pelo usuário")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Erro inesperado durante instalação: {e}")
        sys.exit(1)
